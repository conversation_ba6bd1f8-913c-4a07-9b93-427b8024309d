/*
 * 玩家控制器 - 等距视角移动系统
 */

using Godot;
using ArchipelagoGame.Interfaces;

namespace ArchipelagoGame.Player
{
    /// <summary>
    /// 玩家控制器 - 实现等距视角的角色移动
    /// 继承自CharacterBody2D以获得物理移动和碰撞检测功能
    /// </summary>
    public partial class PlayerController : CharacterBody2D
    {

        [Export] public float Speed = 300.0f; // 移动速度（像素/秒）
        public Node2D Player;
        public AnimationPlayer PlayerAnimation;
        public override void _Ready()
        {

            Player = GetNode<Node2D>("/root/PlayerBehavior/Player");
            PlayerAnimation = GetNode<AnimationPlayer>("/root/PlayerBehavior/Player/AnimationPlayer");
        }


        public override void _PhysicsProcess(double delta)
        {
            // 1. 获取输入方向（-1到1之间的值）
            Vector2 inputDirection = Input.GetVector("ui_left", "ui_right", "ui_up", "ui_down");
            Velocity = inputDirection * Speed;
            MoveAndSlide();

            // 根据移动方向播放动画
            if (inputDirection != Vector2.Zero)
            {
                string animationName = GetAnimationFromDirection(inputDirection);
                PlayerAnimation.Play(animationName);
            }
            else
            {
                // 停止时可以播放idle动画（如果有的话）
                // PlayerAnimation.Play("idle");
            }
        }

        /// <summary>
        /// 根据输入方向获取对应的动画名称
        /// </summary>
        /// <param name="direction">输入方向向量</param>
        /// <returns>动画名称</returns>
        private string GetAnimationFromDirection(Vector2 direction)
        {
            // 计算角度（以弧度为单位）
            float angle = Mathf.Atan2(direction.Y, direction.X);

            // 将弧度转换为度数
            float degrees = Mathf.RadToDeg(angle);

            // 确保角度在0-360度范围内
            if (degrees < 0)
                degrees += 360;

            // 根据角度范围选择动画
            // 每个方向占45度，总共8个方向
            if (degrees >= 337.5f || degrees < 22.5f)
                return "right_move";        // 0度：右
            else if (degrees >= 22.5f && degrees < 67.5f)
                return "up_right_move";     // 45度：右上
            else if (degrees >= 67.5f && degrees < 112.5f)
                return "up_move";           // 90度：上
            else if (degrees >= 112.5f && degrees < 157.5f)
                return "up_left_move";      // 135度：左上
            else if (degrees >= 157.5f && degrees < 202.5f)
                return "left_move";         // 180度：左
            else if (degrees >= 202.5f && degrees < 247.5f)
                return "left_move";         // 225度：左下（使用左移动动画）
            else if (degrees >= 247.5f && degrees < 292.5f)
                return "down_move";         // 270度：下
            else // degrees >= 292.5f && degrees < 337.5f
                return "right_move";        // 315度：右下（使用右移动动画）
        }
    }
}
